//
//  WelcomeView.swift
//  Color Palette X
//
//  Created by <PERSON><PERSON><PERSON> on 2025/7/15.
//

import SwiftUI
import RandomColor

// MARK: - DisplayLink管理器（解决SwiftUI struct限制）
class DisplayLinkManager {
    private var displayLink: CADisplayLink?
    private var lastTimestamp: CFTimeInterval = 0
    var onUpdate: ((CFTimeInterval) -> Void)?

    func start() {
        guard displayLink == nil else { return }

        displayLink = CADisplayLink(target: self, selector: #selector(update))
        displayLink?.add(to: .main, forMode: .common)

        lastTimestamp = CACurrentMediaTime()
        AppLogger.debug("🎬 CADisplayLink 已启动 - 支持自适应刷新率", category: .ui)
    }

    func stop() {
        displayLink?.invalidate()
        displayLink = nil
        AppLogger.debug("⏹️ CADisplayLink 已停止", category: .ui)
    }

    @objc private func update() {
        guard let displayLink = displayLink else { return }

        let currentTime = displayLink.timestamp
        let deltaTime = currentTime - lastTimestamp
        lastTimestamp = currentTime

        onUpdate?(deltaTime)
    }

    deinit {
        stop()
    }
}

/// 随机颜色调色板展示界面
///
/// 主要功能：
/// - 显示100行随机颜色组合
/// - 自动无限垂直滚动（使用CADisplayLink实现像素级滚动）
/// - 触摸暂停滚动功能
/// - 重新生成颜色按钮
/// - 每行包含基准色、互补色、类似色、三色调和色
struct WelcomeView: View {

    // MARK: - 状态属性

    /// 颜色调色板数据
    @State private var colorPalettes: [ColorPaletteRow] = []

    /// ScrollPosition 控制器
    @State private var scrollPosition = ScrollPosition()

    /// CADisplayLink 管理器
     private var displayLinkManager = DisplayLinkManager()

    /// 当前滚动Y位置
    @State private var currentScrollY: CGFloat = 0

    /// 用户手动滚动时的实际偏移量
    @State private var userScrollOffset: CGFloat = 0

    /// 滚动几何信息
    @State private var scrollGeometry: ScrollGeometry?

    /// 是否正在自动滚动
    @State private var isAutoScrolling = false

    /// 是否正在生成新颜色
    @State private var isGenerating = false

    /// 触摸检测状态
    @State private var isTouching = false

    /// 内容总高度（用于计算滚动边界）
    @State private var contentHeight: CGFloat = 0

    /// 可见区域高度
    @State private var visibleHeight: CGFloat = 0
    @State private var isDecelerating = false

    // MARK: - 常量

    /// 调色板总数量
    private let paletteCount = 100

    /// 自动滚动速度（像素/秒）
    private let scrollSpeedPerSecond: CGFloat = 50.0

    // MARK: - 初始化

    init() {
        AppLogger.info("WelcomeView 初始化 - 随机颜色调色板展示界面", category: .ui)
    }

    // MARK: - 视图主体

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                SwiftUIColor(.systemGroupedBackground)
                    .ignoresSafeArea()

                VStack(spacing: 0) {
                    // 主滚动内容区域
                    scrollContentView

                    // 底部控制区域
                    bottomControlView
                }
            }
            .navigationTitle("颜色调色板")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                setupInitialData()
            }
            .onDisappear {
                cleanupResources()
            }
        }
    }

    // MARK: - 子视图组件

    /// 滚动内容视图
    private var scrollContentView: some View {
        ScrollView(.vertical, showsIndicators: false) {
            LazyVStack(spacing: 16) {
                // 顶部间距
                SwiftUIColor.clear.frame(height: 8)

                // 颜色调色板行
                ForEach(Array(colorPalettes.enumerated()), id: \.element.id) { index, palette in
                    ColorPaletteRowView(
                        paletteRow: palette,
                        squareSize: 70,
                        showDetails: true
                    )
                    .id("palette_\(index)")
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .move(edge: .top).combined(with: .opacity)
                    ))
                    .onAppear {
//                        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
//                        AppLogger.debug("📱 调色板行 [\(index)] (ID: \(palette.id)) 出现在屏幕上 - \(timestamp)", category: .ui)
                    }
                    .onDisappear {
//                        let timestamp = DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .medium)
//                        AppLogger.debug("👻 调色板行 [\(index)] (ID: \(palette.id)) 从屏幕消失 - \(timestamp)", category: .ui)
                    }
                }

                // 底部间距
                Spacer().frame(height: 100)
            }
            .padding(.horizontal, 8)
            .background(
                GeometryReader { geometry in
                    SwiftUIColor.clear
                        .onAppear {
                            contentHeight = geometry.size.height
                        }
                        .onChange(of: geometry.size.height) { _, newHeight in
                            contentHeight = newHeight
                        }
                }
            )
        }
        .scrollPosition($scrollPosition)
        .onScrollPhaseChange { _, newPhase in
            isDecelerating = newPhase == .decelerating
        }
        .onChange(of: isDecelerating) { oldValue, newValue in
            // AppLogger.debug("🎯 减速状态变化: \(oldValue) -> \(newValue), isTouching: \(isTouching), isAutoScrolling: \(isAutoScrolling)", category: .ui)
            if !newValue && !isTouching && !isAutoScrolling {
//                syncCurrentScrollPosition()
                // AppLogger.debug("🚀 减速结束，从位置 \(currentScrollY) 启动自动滚动", category: .ui)
                startAutoScroll()
            }
        }
        .onScrollGeometryChange(for: ScrollGeometry.self) { geometry in
            return geometry
        } action: { _, newGeometry in
            scrollGeometry = newGeometry
            AppLogger.debug("xxx 用户滚动到位置: \(newGeometry.contentOffset.y)", category: .ui)
            // 监听用户手动滚动，更新实际偏移量
//            if isTouching {
//                // contentOffset.y 是负值，转换为正值存储
//                userScrollOffset = -newGeometry.contentOffset.y
////                AppLogger.debug("📍 用户滚动到位置: \(userScrollOffset)", category: .ui)
//            }
//            // 如果不是触摸状态且不是自动滚动，同步当前位置（处理程序化滚动）
//            else if !isAutoScrolling {
//                currentScrollY = -newGeometry.contentOffset.y
//            }
        }
        .background(
            GeometryReader { geometry in
                SwiftUIColor.clear
                    .onAppear {
                        visibleHeight = geometry.size.height
                    }
                    .onChange(of: geometry.size.height) { _, newHeight in
                        visibleHeight = newHeight
                    }
            }
        )
        .onDisappear {
            stopAutoScroll()
        }
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    handleTouchStart()
                }
                .onEnded { _ in
                    handleTouchEnd()
                }
        )
    }

    /// 底部控制视图
    private var bottomControlView: some View {
        VStack(spacing: 12) {
            // 状态指示器
            HStack(spacing: 16) {
                // 自动滚动状态
                HStack(spacing: 6) {
                    Circle()
                        .fill(isAutoScrolling ? .green : .gray)
                        .frame(width: 8, height: 8)
                        .animation(.easeInOut(duration: 0.3), value: isAutoScrolling)

                    Text(isAutoScrolling ? "自动滚动中" : "滚动已暂停")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.secondary)
                }

                Spacer()

                // 调色板数量
                Text("\(colorPalettes.count) 个调色板")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.secondary)
            }

            // 重新生成按钮
            Button(action: {
                regenerateColors()
            }) {
                HStack(spacing: 8) {
                    if isGenerating {
                        ProgressView()
                            .scaleEffect(0.8)
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 16, weight: .medium))
                    }

                    Text(isGenerating ? "生成中..." : "重新生成颜色")
                        .font(.system(size: 16, weight: .semibold))
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(
                    LinearGradient(
                        colors: [.blue, .purple],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .clipShape(RoundedRectangle(cornerRadius: 25))
                .shadow(
                    color: .blue.opacity(0.3),
                    radius: 8,
                    x: 0,
                    y: 4
                )
            }
            .disabled(isGenerating)
            .scaleEffect(isGenerating ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isGenerating)
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 16)
        .background(
            .ultraThinMaterial,
            in: RoundedRectangle(cornerRadius: 0)
        )
    }

    // MARK: - 数据管理方法

    /// 设置初始数据
    private func setupInitialData() {
        AppLogger.info("开始设置初始颜色调色板数据", category: .ui)

        withAnimation(.easeInOut(duration: 0.5)) {
            colorPalettes = ColorPaletteRow.generateRandomPalettes(count: paletteCount)
        }

        AppLogger.info("初始数据设置完成，共 \(colorPalettes.count) 个调色板", category: .ui)

        // 延迟启动自动滚动，确保视图已完全加载
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            self.startAutoScroll()
        }
    }

    /// 重新生成颜色
    private func regenerateColors() {
        guard !isGenerating else { return }

        AppLogger.info("开始重新生成颜色调色板", category: .ui)

        isGenerating = true

        // 停止自动滚动
        stopAutoScroll()

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()

        // 生成新颜色
        let newPalettes = ColorPaletteRow.generateRandomPalettes(count: paletteCount)

        guard !newPalettes.isEmpty else {
            AppLogger.error("生成的颜色调色板为空", category: .ui)
            isGenerating = false
            return
        }

        // 重置滚动位置
        currentScrollY = 0
        scrollPosition.scrollTo(y: 0)

        withAnimation(.easeInOut(duration: 0.8)) {
            colorPalettes = newPalettes
        }

        // 延迟恢复自动滚动
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.isGenerating = false
            self.startAutoScroll()
        }

        AppLogger.info("颜色重新生成完成", category: .ui)
    }

    // MARK: - 自动滚动控制方法

    /// 开始自动滚动 - 使用CADisplayLink实现VSync同步的流畅滚动
    private func startAutoScroll() {
        guard !isTouching && !isAutoScrolling && !colorPalettes.isEmpty else { return }

        AppLogger.debug("🚀 开始CADisplayLink驱动的自动滚动", category: .ui)

        isAutoScrolling = true

        // 设置DisplayLink的更新回调
        displayLinkManager.onUpdate = { [self] deltaTime in
            updateScroll(deltaTime: deltaTime)
        }

        displayLinkManager.start()
    }

    /// 停止自动滚动
    private func stopAutoScroll() {
        AppLogger.debug("⏹️ 停止CADisplayLink自动滚动", category: .ui)

        isAutoScrolling = false
        displayLinkManager.stop()
        displayLinkManager.onUpdate = nil
    }

    /// 更新滚动位置 - 基于真实时间差的精确像素级滚动
    private func updateScroll(deltaTime: CFTimeInterval) {
        guard isAutoScrolling && !isTouching else { return }
        guard !colorPalettes.isEmpty else { return }
        guard contentHeight > 0 && visibleHeight > 0 else { return }

        // 基于实际时间差计算滚动距离，确保恒定速度
        let scrollDelta = scrollSpeedPerSecond * deltaTime
        currentScrollY += scrollDelta

        // 计算最大滚动距离（内容高度 - 可见高度）
        let maxScrollY = contentHeight - visibleHeight

        // 当滚动到底部时，重新生成颜色并从头开始
        if currentScrollY >= maxScrollY {
            AppLogger.info("🔄 滚动到底部，重新生成颜色并从头开始", category: .ui)
            regenerateColorsAndRestart()
            return
        }

        // 直接设置位置，利用VSync同步确保流畅度
        // 注意：scrollTo(y:) 期望的是正值，表示向下滚动的距离
//        scrollPosition.scrollTo(y: currentScrollY)

//        AppLogger.debug("🎯 自动滚动到位置: \(currentScrollY)", category: .ui)
    }

    /// 重新生成颜色并重新开始滚动
    private func regenerateColorsAndRestart() {
        // 暂停自动滚动
        stopAutoScroll()

        // 重置滚动位置
        currentScrollY = 0
        scrollPosition.scrollTo(y: 0)

        // 生成新的颜色调色板
        withAnimation(.easeInOut(duration: 0.5)) {
            colorPalettes = ColorPaletteRow.generateRandomPalettes(count: paletteCount)
        }

        AppLogger.info("重新生成了 \(colorPalettes.count) 个新颜色调色板", category: .ui)

        // 延迟重新开始自动滚动
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            self.startAutoScroll()
        }
    }

    // MARK: - 资源管理方法

    /// 清理资源，防止内存泄漏
    private func cleanupResources() {
        AppLogger.info("开始清理 WelcomeView 资源", category: .ui)

        // 停止自动滚动
        stopAutoScroll()

        // 清理状态
        isAutoScrolling = false
        isTouching = false
        isGenerating = false
        currentScrollY = 0

        AppLogger.info("WelcomeView 资源清理完成", category: .ui)
    }

    // MARK: - 辅助方法

    /// 同步当前滚动位置 - 确保各种状态下的位置一致性
//    private func syncCurrentScrollPosition() {
//        if let geometry = scrollGeometry {
//            let newPosition = geometry.contentOffset.y
//            let positionDiff = abs(newPosition - currentScrollY)
//            AppLogger.debug("🔄 同步前: currentScrollY=\(currentScrollY), geometry.contentOffset.y=\(newPosition), diff=\(positionDiff)", category: .ui)
//            if positionDiff > 1.0 { // 只有显著差异时才更新
//                currentScrollY = newPosition
//                AppLogger.debug("🔄 同步滚动位置: \(currentScrollY)", category: .ui)
//            }
//        }
//    }

    // MARK: - 手势处理方法

    /// 处理触摸开始 - 手指按下暂停自动滚动
    private func handleTouchStart() {
        guard !isTouching else { return }

        AppLogger.debug("👆 检测到触摸开始，暂停自动滚动", category: .ui)

        // 先停止自动滚动
        stopAutoScroll()

        // 同步当前滚动位置
//        syncCurrentScrollPosition()
        userScrollOffset = currentScrollY

        // 设置触摸状态
        isTouching = true

        AppLogger.debug("📍 触摸开始时的位置: \(currentScrollY)", category: .ui)
    }

    /// 处理触摸结束 - 松开手指恢复自动滚动
    private func handleTouchEnd() {
        guard isTouching else { return }

//        syncCurrentScrollPosition()

        AppLogger.debug("👆 触摸结束，从位置 \(currentScrollY) 恢复自动滚动，isDecelerating: \(isDecelerating)", category: .ui)

        isTouching = false

        if !isDecelerating {
            AppLogger.debug("🚀 无减速阶段，立即启动自动滚动", category: .ui)
            startAutoScroll()
        }
    }
}

// MARK: - 功能特色卡片（保留原有组件）
struct FeatureCard: View {
    let icon: String
    let title: LocalizedStringKey
    let description: LocalizedStringKey
    let color: SwiftUIColor

    var body: some View {
        VStack(spacing: 12) {
            // 图标
            ZStack {
                Circle()
                    .fill(color.opacity(0.15))
                    .frame(width: 40, height: 40)

                Image(systemName: icon)
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(color)
            }

            VStack(spacing: 4) {
                Text(title)
                    .font(.system(size: 14, weight: .semibold))
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(description)
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .lineLimit(3)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 120)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(SwiftUIColor(.systemBackground))
                .shadow(
                    color: color.opacity(0.1),
                    radius: 8,
                    x: 0,
                    y: 4
                )
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(color.opacity(0.2), lineWidth: 1)
        )
    }
}

// MARK: - 预览

#Preview {
    WelcomeView()
}


