---
type: "always_apply"
---

1. 这是一个IOS 工程
2. 你总是使用 SwiftUI 和 Swift
3. 数据存储数据使用 SwiftData 和@AppStorage
4. SwiftUI 状态管理全部使用@Observable
5. 你总是以中文回答
6. 多线程全部使用 Swift concurrency
7. 字符串必须需要符合 IOS 国际化标准.比如字符串英文你就要翻译对应的语言.中文就对应的翻译
8.确保布局对不同 iOS 屏幕尺寸具有响应性和适应性。
10. 一定要为 写SwiftUI 的#preview
11. 架构要符合 MVC,不要全部混杂在一个文件中
12. 不同业务的 SwiftUI 一定要放在不同的文件下
14. 日志系统全部使用Logger
15. 导航使用NavigationStack来实现
16. 编译命令:`xcodebuild build -project "/工程目录/Color Palette X.xcodeproj" -scheme "Color Palette X" -destination 'platform=iOS Simulator,name=iPhone 16'`