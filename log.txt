[2025-07-21 22:36:31.048] [🐛 DEBUG] [📝General] [Color_Palette_XApp.swift:43] 日志系统配置完成
[2025-07-21 22:36:31.048] [🐛 DEBUG] [📝General] [Color_Palette_XApp.swift:43] 日志系统配置完成
[2025-07-21 22:36:31.069] [ℹ️ INFO] [📝General] [Color_Palette_XApp.swift:16] Color Palette X 应用启动
[2025-07-21 22:36:31.069] [ℹ️ INFO] [📝General] [Color_Palette_XApp.swift:16] Color Palette X 应用启动
[2025-07-21 22:36:31.125] [ℹ️ INFO] [🎨UI] [ContentView.swift:14] 应用主界面初始化
[2025-07-21 22:36:31.125] [ℹ️ INFO] [🎨UI] [ContentView.swift:14] 应用主界面初始化
[2025-07-21 22:36:31.127] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:105] WelcomeView 初始化 - 随机颜色调色板展示界面
[2025-07-21 22:36:31.127] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:105] WelcomeView 初始化 - 随机颜色调色板展示界面
[2025-07-21 22:36:31.127] [ℹ️ INFO] [🎨UI] [HomeView.swift:13] HomeView 初始化
[2025-07-21 22:36:31.127] [ℹ️ INFO] [🎨UI] [HomeView.swift:13] HomeView 初始化
[2025-07-21 22:36:31.167] [ℹ️ INFO] [🎨UI] [Color_Palette_XApp.swift:23] 主窗口显示
[2025-07-21 22:36:31.167] [ℹ️ INFO] [🎨UI] [Color_Palette_XApp.swift:23] 主窗口显示
[2025-07-21 22:36:31.232] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.232] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.232] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:312] 开始设置初始颜色调色板数据
[2025-07-21 22:36:31.232] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:312] 开始设置初始颜色调色板数据
[2025-07-21 22:36:31.232] [ℹ️ INFO] [📝General] [ColorPaletteRow.swift:132] 开始批量生成 100 个随机颜色组合
[2025-07-21 22:36:31.232] [ℹ️ INFO] [📝General] [ColorPaletteRow.swift:132] 开始批量生成 100 个随机颜色组合
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E82027
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E82027
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9944444444444445, 新色相: 0.49444444444444446
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9944444444444445, 新色相: 0.49444444444444446
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.232] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9114444444444445
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9114444444444445
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6614444444444445
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6614444444444445
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E82027
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E82027
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F7C9B2
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F7C9B2
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.05555555555555556, 新色相: 0.5555555555555556
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.05555555555555556, 新色相: 0.5555555555555556
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9725555555555555
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9725555555555555
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.233] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7225555555555556
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7225555555555556
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F7C9B2
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F7C9B2
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E8B604
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E8B604
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.13055555555555556, 新色相: 0.6305555555555555
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.13055555555555556, 新色相: 0.6305555555555555
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.21355555555555555
[2025-07-21 22:36:31.234] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.21355555555555555
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7975555555555556
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7975555555555556
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E8B604
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E8B604
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #2F9343
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #2F9343
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.36666666666666664, 新色相: 0.8666666666666667
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.36666666666666664, 新色相: 0.8666666666666667
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.44966666666666666
[2025-07-21 22:36:31.235] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.44966666666666666
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.033666666666666734
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.033666666666666734
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #2F9343
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #2F9343
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #54A4C4
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #54A4C4
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5472222222222223, 新色相: 0.047222222222222276
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5472222222222223, 新色相: 0.047222222222222276
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6302222222222222
[2025-07-21 22:36:31.236] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6302222222222222
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.8802222222222222
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.8802222222222222
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #54A4C4
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #54A4C4
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C699F7
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C699F7
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7472222222222222, 新色相: 0.24722222222222223
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7472222222222222, 新色相: 0.24722222222222223
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6642222222222223
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6642222222222223
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.41422222222222227
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.41422222222222227
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C699F7
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C699F7
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C4035A
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C4035A
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9250000000000002, 新色相: 0.42500000000000027
[2025-07-21 22:36:31.237] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9250000000000002, 新色相: 0.42500000000000027
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8420000000000002
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8420000000000002
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5920000000000001
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5920000000000001
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C4035A
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C4035A
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0D0026
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0D0026
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.725, 新色相: 0.2250000000000001
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.725, 新色相: 0.2250000000000001
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8079999999999999
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8079999999999999
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3919999999999999
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3919999999999999
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0D0026
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0D0026
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.238] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #27F96D
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #27F96D
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.3888888888888889, 新色相: 0.8888888888888888
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.3888888888888889, 新色相: 0.8888888888888888
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.4718888888888889
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.4718888888888889
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.7218888888888889
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.7218888888888889
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #27F96D
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #27F96D
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9A298
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9A298
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.01666666666666668, 新色相: 0.5166666666666667
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.01666666666666668, 新色相: 0.5166666666666667
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.09966666666666668
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.09966666666666668
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6836666666666668
[2025-07-21 22:36:31.239] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6836666666666668
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9A298
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9A298
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #DD5A13
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #DD5A13
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.05833333333333335, 新色相: 0.5583333333333333
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.05833333333333335, 新色相: 0.5583333333333333
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9753333333333334
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9753333333333334
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #DD5A13
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #DD5A13
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #6B6B6B
[2025-07-21 22:36:31.240] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #6B6B6B
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #6B6B6B
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #6B6B6B
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #19C469
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #19C469
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.41111111111111115, 新色相: 0.9111111111111112
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.41111111111111115, 新色相: 0.9111111111111112
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.49411111111111117
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.49411111111111117
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.7441111111111112
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.7441111111111112
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #19C469
[2025-07-21 22:36:31.241] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #19C469
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C2D0F9
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C2D0F9
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.625, 新色相: 0.125
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.625, 新色相: 0.125
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.542
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.542
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.958
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.958
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C2D0F9
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C2D0F9
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #370672
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #370672
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7416666666666667, 新色相: 0.2416666666666667
[2025-07-21 22:36:31.242] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7416666666666667, 新色相: 0.2416666666666667
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6586666666666667
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6586666666666667
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.40866666666666673
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.40866666666666673
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #370672
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #370672
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F2B0E9
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F2B0E9
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8555555555555555, 新色相: 0.3555555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8555555555555555, 新色相: 0.3555555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7725555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7725555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1885555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1885555555555556
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F2B0E9
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F2B0E9
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3D3D3D
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3D3D3D
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.243] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3D3D3D
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3D3D3D
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #ECF995
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #ECF995
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.18888888888888888, 新色相: 0.6888888888888889
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.18888888888888888, 新色相: 0.6888888888888889
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.10588888888888888
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.10588888888888888
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8558888888888889
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8558888888888889
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #ECF995
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #ECF995
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.244] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #A50D19
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #A50D19
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9861111111111112, 新色相: 0.48611111111111116
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9861111111111112, 新色相: 0.48611111111111116
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9031111111111112
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9031111111111112
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3191111111111111
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3191111111111111
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #A50D19
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #A50D19
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.245] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EDD861
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EDD861
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.14166666666666664, 新色相: 0.6416666666666666
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.14166666666666664, 新色相: 0.6416666666666666
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.22466666666666663
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.22466666666666663
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8086666666666666
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8086666666666666
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EDD861
[2025-07-21 22:36:31.246] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EDD861
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #80ED6D
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #80ED6D
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.30833333333333335, 新色相: 0.8083333333333333
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.30833333333333335, 新色相: 0.8083333333333333
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.6413333333333333
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.6413333333333333
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #80ED6D
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #80ED6D
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #110487
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #110487
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6833333333333332, 新色相: 0.18333333333333313
[2025-07-21 22:36:31.247] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6833333333333332, 新色相: 0.18333333333333313
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.7663333333333332
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.7663333333333332
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3503333333333334
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3503333333333334
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #110487
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #110487
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #684B89
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #684B89
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7444444444444445, 新色相: 0.24444444444444446
[2025-07-21 22:36:31.248] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7444444444444445, 新色相: 0.24444444444444446
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6614444444444445
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6614444444444445
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.07744444444444443
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.07744444444444443
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #684B89
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #684B89
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EF07F7
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EF07F7
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8277777777777778, 新色相: 0.32777777777777795
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8277777777777778, 新色相: 0.32777777777777795
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7447777777777779
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7447777777777779
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1607777777777779
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1607777777777779
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EF07F7
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EF07F7
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #A3A3A3
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #A3A3A3
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.249] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #A3A3A3
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #A3A3A3
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #91010F
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #91010F
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9833333333333333, 新色相: 0.4833333333333334
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9833333333333333, 新色相: 0.4833333333333334
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.250] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.06633333333333336
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.06633333333333336
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6503333333333332
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6503333333333332
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #91010F
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #91010F
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #332626
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #332626
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #332626
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #332626
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EA8F5B
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EA8F5B
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.061111111111111116, 新色相: 0.5611111111111111
[2025-07-21 22:36:31.251] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.061111111111111116, 新色相: 0.5611111111111111
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9781111111111112
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9781111111111112
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.39411111111111113
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.39411111111111113
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EA8F5B
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EA8F5B
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F7EA74
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F7EA74
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.15, 新色相: 0.65
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.15, 新色相: 0.65
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.252] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.06699999999999999
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.06699999999999999
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.483
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.483
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F7EA74
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F7EA74
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1C7C04
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1C7C04
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.3, 新色相: 0.8
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.3, 新色相: 0.8
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.383
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.383
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.261] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.633
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.633
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1C7C04
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1C7C04
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #010202
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #010202
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5694444444444444, 新色相: 0.06944444444444442
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5694444444444444, 新色相: 0.06944444444444442
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.4864444444444444
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.4864444444444444
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9024444444444444
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9024444444444444
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #010202
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #010202
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9633E8
[2025-07-21 22:36:31.262] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9633E8
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7583333333333333, 新色相: 0.2583333333333333
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7583333333333333, 新色相: 0.2583333333333333
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6753333333333333
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6753333333333333
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.42533333333333334
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.42533333333333334
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9633E8
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9633E8
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F481D0
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F481D0
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.263] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8861111111111111, 新色相: 0.38611111111111107
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8861111111111111, 新色相: 0.38611111111111107
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.969111111111111
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.969111111111111
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5531111111111111
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5531111111111111
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F481D0
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F481D0
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.264] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0B1409
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0B1409
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.30833333333333335, 新色相: 0.8083333333333333
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.30833333333333335, 新色相: 0.8083333333333333
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.39133333333333337
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.6413333333333333
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.6413333333333333
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0B1409
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0B1409
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B73935
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B73935
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.265] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.005555555555555554, 新色相: 0.5055555555555555
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.005555555555555554, 新色相: 0.5055555555555555
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9225555555555556
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9225555555555556
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6725555555555556
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6725555555555556
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B73935
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B73935
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9E4B1
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9E4B1
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.11666666666666665, 新色相: 0.6166666666666667
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.11666666666666665, 新色相: 0.6166666666666667
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.03366666666666665
[2025-07-21 22:36:31.266] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.03366666666666665
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.44966666666666666
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.44966666666666666
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9E4B1
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9E4B1
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E8C209
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E8C209
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.1388888888888889, 新色相: 0.6388888888888888
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.1388888888888889, 新色相: 0.6388888888888888
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2218888888888889
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2218888888888889
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8058888888888889
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8058888888888889
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E8C209
[2025-07-21 22:36:31.267] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E8C209
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1B8752
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1B8752
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.41944444444444445, 新色相: 0.9194444444444445
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.41944444444444445, 新色相: 0.9194444444444445
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.33644444444444443
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.33644444444444443
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.08644444444444455
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.08644444444444455
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1B8752
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1B8752
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #043A91
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #043A91
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6027777777777777, 新色相: 0.10277777777777786
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6027777777777777, 新色相: 0.10277777777777786
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5197777777777778
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5197777777777778
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.26977777777777767
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.26977777777777767
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #043A91
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #043A91
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B69DF2
[2025-07-21 22:36:31.268] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B69DF2
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7166666666666667, 新色相: 0.21666666666666679
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7166666666666667, 新色相: 0.21666666666666679
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.7996666666666666
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.7996666666666666
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3836666666666666
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3836666666666666
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B69DF2
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B69DF2
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D6086F
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D6086F
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.269] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9166666666666666, 新色相: 0.4166666666666665
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9166666666666666, 新色相: 0.4166666666666665
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9996666666666666
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9996666666666666
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.2496666666666667
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.2496666666666667
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D6086F
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D6086F
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3C3D3B
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3C3D3B
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.26666666666666644, 新色相: 0.7666666666666664
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.26666666666666644, 新色相: 0.7666666666666664
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.18366666666666642
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.18366666666666642
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.9336666666666664
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.9336666666666664
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3C3D3B
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3C3D3B
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0A931E
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #0A931E
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.35833333333333334, 新色相: 0.8583333333333334
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.35833333333333334, 新色相: 0.8583333333333334
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.44133333333333336
[2025-07-21 22:36:31.270] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.44133333333333336
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.02533333333333343
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.02533333333333343
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0A931E
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #0A931E
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F4A897
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F4A897
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.030555555555555575, 新色相: 0.5305555555555556
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.030555555555555575, 新色相: 0.5305555555555556
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.11355555555555558
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.11355555555555558
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3635555555555556
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3635555555555556
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F4A897
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F4A897
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #DB7611
[2025-07-21 22:36:31.271] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #DB7611
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.08333333333333333, 新色相: 0.5833333333333334
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.08333333333333333, 新色相: 0.5833333333333334
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.16633333333333333
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.16633333333333333
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7503333333333334
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7503333333333334
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #DB7611
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #DB7611
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #CCB85F
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #CCB85F
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.1361111111111111, 新色相: 0.6361111111111111
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.1361111111111111, 新色相: 0.6361111111111111
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2191111111111111
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2191111111111111
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.46911111111111115
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.46911111111111115
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #CCB85F
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #CCB85F
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.272] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1FC413
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1FC413
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.32222222222222224, 新色相: 0.8222222222222222
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.32222222222222224, 新色相: 0.8222222222222222
[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
<OnScrollGeometryChange Modifier> tried to update multiple times per frame.

[2025-07-21 22:36:31.273] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.23922222222222222
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.23922222222222222
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.9892222222222222
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.9892222222222222
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1FC413
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1FC413
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.296] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #84F2F9
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #84F2F9
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5111111111111111, 新色相: 0.011111111111111072
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5111111111111111, 新色相: 0.011111111111111072
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.42811111111111105
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.42811111111111105
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.844111111111111
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.844111111111111
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #84F2F9
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #84F2F9
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #56078E
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #56078E
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7638888888888888, 新色相: 0.26388888888888884
[2025-07-21 22:36:31.297] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7638888888888888, 新色相: 0.26388888888888884
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8468888888888888
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8468888888888888
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.0968888888888888
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.0968888888888888
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #56078E
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #56078E
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C08BC9
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C08BC9
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8083333333333332, 新色相: 0.3083333333333331
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8083333333333332, 新色相: 0.3083333333333331
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8913333333333332
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8913333333333332
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.298] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1413333333333333
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1413333333333333
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C08BC9
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C08BC9
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #565656
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #565656
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
onChange(of: CGFloat) action tried to update multiple times per frame.
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.299] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #565656
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #565656
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BBDCF7
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BBDCF7
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5749999999999998, 新色相: 0.07499999999999973
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5749999999999998, 新色相: 0.07499999999999973
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.4919999999999998
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.4919999999999998
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9079999999999999
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9079999999999999
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BBDCF7
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BBDCF7
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B20323
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #B20323
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9694444444444444, 新色相: 0.46944444444444455
[2025-07-21 22:36:31.300] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9694444444444444, 新色相: 0.46944444444444455
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.052444444444444516
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.052444444444444516
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3024444444444445
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3024444444444445
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B20323
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #B20323
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #8C7331
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #8C7331
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.12222222222222223, 新色相: 0.6222222222222222
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.12222222222222223, 新色相: 0.6222222222222222
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.20522222222222225
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.20522222222222225
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.45522222222222225
[2025-07-21 22:36:31.301] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.45522222222222225
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #8C7331
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #8C7331
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9FC50
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9FC50
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16944444444444443, 新色相: 0.6694444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16944444444444443, 新色相: 0.6694444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.08644444444444442
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.08644444444444442
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8364444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8364444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9FC50
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9FC50
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E6F791
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E6F791
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.19444444444444445, 新色相: 0.6944444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.19444444444444445, 新色相: 0.6944444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.27744444444444444
[2025-07-21 22:36:31.302] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.27744444444444444
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8614444444444445
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8614444444444445
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E6F791
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E6F791
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #052160
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #052160
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6166666666666667, 新色相: 0.1166666666666667
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6166666666666667, 新色相: 0.1166666666666667
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6996666666666667
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6996666666666667
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.28366666666666673
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.28366666666666673
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #052160
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #052160
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #7A04DB
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #7A04DB
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.303] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7583333333333333, 新色相: 0.2583333333333333
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7583333333333333, 新色相: 0.2583333333333333
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6753333333333333
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6753333333333333
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.09133333333333327
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.09133333333333327
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #7A04DB
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #7A04DB
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F72CD2
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F72CD2
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8638888888888889, 新色相: 0.36388888888888893
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8638888888888889, 新色相: 0.36388888888888893
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.780888888888889
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.780888888888889
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1968888888888889
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.1968888888888889
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F72CD2
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F72CD2
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.304] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BCBCBC
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BCBCBC
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BCBCBC
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BCBCBC
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #05496B
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #05496B
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5555555555555556, 新色相: 0.05555555555555558
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5555555555555556, 新色相: 0.05555555555555558
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.47255555555555556
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.47255555555555556
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.8885555555555555
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.8885555555555555
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #05496B
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #05496B
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.305] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #110302
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #110302
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.01111111111111109, 新色相: 0.5111111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.01111111111111109, 新色相: 0.5111111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9281111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9281111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6781111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6781111111111111
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #110302
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #110302
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D6A431
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D6A431
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.11666666666666664, 新色相: 0.6166666666666667
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.11666666666666664, 新色相: 0.6166666666666667
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.033666666666666636
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.033666666666666636
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7836666666666667
[2025-07-21 22:36:31.306] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7836666666666667
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D6A431
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D6A431
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FCFCB0
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FCFCB0
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16666666666666666, 新色相: 0.6666666666666666
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16666666666666666, 新色相: 0.6666666666666666
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.24966666666666665
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.24966666666666665
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8336666666666667
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8336666666666667
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FCFCB0
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FCFCB0
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #8CA30B
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #8CA30B
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.19166666666666668, 新色相: 0.6916666666666667
[2025-07-21 22:36:31.307] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.19166666666666668, 新色相: 0.6916666666666667
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.10866666666666668
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.10866666666666668
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.5246666666666667
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.5246666666666667
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #8CA30B
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #8CA30B
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #626368
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #626368
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6416666666666669, 新色相: 0.14166666666666705
[2025-07-21 22:36:31.308] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6416666666666669, 新色相: 0.14166666666666705
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.558666666666667
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.558666666666667
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.974666666666667
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.974666666666667
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #626368
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #626368
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9731F7
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9731F7
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7527777777777778, 新色相: 0.25277777777777777
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7527777777777778, 新色相: 0.25277777777777777
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6697777777777778
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.6697777777777778
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.4197777777777778
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.4197777777777778
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9731F7
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9731F7
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FA92FC
[2025-07-21 22:36:31.309] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FA92FC
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8305555555555556, 新色相: 0.3305555555555557
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8305555555555556, 新色相: 0.3305555555555557
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9135555555555556
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9135555555555556
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.16355555555555568
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.16355555555555568
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FA92FC
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FA92FC
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1C1C1C
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #1C1C1C
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.310] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1C1C1C
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #1C1C1C
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.667
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #910A02
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #910A02
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.00833333333333334, 新色相: 0.5083333333333333
[2025-07-21 22:36:31.311] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.00833333333333334, 新色相: 0.5083333333333333
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.09133333333333335
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.09133333333333335
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3413333333333334
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3413333333333334
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #910A02
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #910A02
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.312] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FCD5AB
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #FCD5AB
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0861111111111111, 新色相: 0.5861111111111111
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0861111111111111, 新色相: 0.5861111111111111
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.313] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.1691111111111111
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.1691111111111111
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.4191111111111111
[2025-07-21 22:36:31.314] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.4191111111111111
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FCD5AB
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #FCD5AB
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EFE704
[2025-07-21 22:36:31.317] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EFE704
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16111111111111112, 新色相: 0.6611111111111111
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16111111111111112, 新色相: 0.6611111111111111
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.07811111111111112
[2025-07-21 22:36:31.318] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.07811111111111112
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8281111111111111
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8281111111111111
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EFE704
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EFE704
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #34A88D
[2025-07-21 22:36:31.319] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #34A88D
[2025-07-21 22:36:31.320] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.320] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.4611111111111111, 新色相: 0.961111111111111
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.4611111111111111, 新色相: 0.961111111111111
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.37811111111111106
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.37811111111111106
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.321] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.12811111111111106
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.12811111111111106
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #34A88D
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #34A88D
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #5864E2
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #5864E2
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6527777777777778, 新色相: 0.15277777777777768
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6527777777777778, 新色相: 0.15277777777777768
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5697777777777778
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5697777777777778
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.322] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.31977777777777794
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.31977777777777794
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #5864E2
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #5864E2
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BB90F4
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #BB90F4
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7388888888888889, 新色相: 0.23888888888888893
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7388888888888889, 新色相: 0.23888888888888893
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.655888888888889
[2025-07-21 22:36:31.323] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.655888888888889
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.40588888888888897
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.40588888888888897
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BB90F4
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #BB90F4
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.324] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E809A5
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E809A5
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8833333333333333, 新色相: 0.3833333333333333
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8833333333333333, 新色相: 0.3833333333333333
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8003333333333333
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8003333333333333
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5503333333333333
[2025-07-21 22:36:31.325] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.5503333333333333
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E809A5
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E809A5
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #000000
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.326] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.327] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #000000
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3263BC
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #3263BC
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6083333333333333, 新色相: 0.10833333333333339
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6083333333333333, 新色相: 0.10833333333333339
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5253333333333333
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5253333333333333
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9413333333333334
[2025-07-21 22:36:31.328] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9413333333333334
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3263BC
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #3263BC
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9BBAE
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #F9BBAE
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.02777777777777779, 新色相: 0.5277777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.02777777777777779, 新色相: 0.5277777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9447777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9447777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3607777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3607777777777778
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9BBAE
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #F9BBAE
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E28902
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E28902
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.10000000000000002, 新色相: 0.6
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.10000000000000002, 新色相: 0.6
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.017000000000000015
[2025-07-21 22:36:31.329] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.017000000000000015
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.43300000000000005
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.43300000000000005
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E28902
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E28902
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #AFAA6D
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #AFAA6D
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.15277777777777782, 新色相: 0.6527777777777778
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.15277777777777782, 新色相: 0.6527777777777778
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2357777777777778
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.2357777777777778
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.48577777777777786
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.48577777777777786
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #AFAA6D
[2025-07-21 22:36:31.330] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #AFAA6D
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #62890C
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #62890C
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.21944444444444444, 新色相: 0.7194444444444444
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.21944444444444444, 新色相: 0.7194444444444444
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.30244444444444446
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.30244444444444446
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8864444444444445
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8864444444444445
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #62890C
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #62890C
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #98B6EA
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #98B6EA
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6055555555555555, 新色相: 0.10555555555555562
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.6055555555555555, 新色相: 0.10555555555555562
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5225555555555556
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.5225555555555556
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.27255555555555544
[2025-07-21 22:36:31.331] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.27255555555555544
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #98B6EA
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #98B6EA
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #6F099E
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #6F099E
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7805555555555556, 新色相: 0.28055555555555545
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7805555555555556, 新色相: 0.28055555555555545
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8635555555555555
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8635555555555555
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.11355555555555563
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.11355555555555563
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #6F099E
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #6F099E
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E3C2EA
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E3C2EA
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8027777777777777, 新色相: 0.3027777777777776
[2025-07-21 22:36:31.332] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8027777777777777, 新色相: 0.3027777777777776
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8857777777777777
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8857777777777777
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.13577777777777778
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.13577777777777778
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E3C2EA
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E3C2EA
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #757575
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #757575
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.083
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #757575
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #757575
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.333] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D575EF
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #D575EF
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7972222222222222, 新色相: 0.29722222222222205
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.7972222222222222, 新色相: 0.29722222222222205
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7142222222222222
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.7142222222222222
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.13022222222222224
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.13022222222222224
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D575EF
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #D575EF
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9E0414
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9E0414
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9833333333333333, 新色相: 0.4833333333333334
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9833333333333333, 新色相: 0.4833333333333334
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.06633333333333336
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.06633333333333336
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.334] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6503333333333332
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.6503333333333332
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9E0414
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9E0414
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #514033
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #514033
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.07222222222222223, 新色相: 0.5722222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.07222222222222223, 新色相: 0.5722222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9892222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.9892222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7392222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.7392222222222222
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #514033
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #514033
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C9C90A
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #C9C90A
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16666666666666666, 新色相: 0.6666666666666666
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.16666666666666666, 新色相: 0.6666666666666666
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.335] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.24966666666666665
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.24966666666666665
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8336666666666667
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.8336666666666667
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C9C90A
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #C9C90A
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E9FC9F
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #E9FC9F
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.20000000000000007, 新色相: 0.7000000000000001
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.20000000000000007, 新色相: 0.7000000000000001
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.11700000000000006
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.11700000000000006
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.5330000000000001
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.5330000000000001
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E9FC9F
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #E9FC9F
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #074377
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #074377
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.336] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5777777777777777, 新色相: 0.07777777777777772
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.5777777777777777, 新色相: 0.07777777777777772
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6607777777777777
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.6607777777777777
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9107777777777777
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.9107777777777777
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #074377
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #074377
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #523F75
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #523F75
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.725, 新色相: 0.2250000000000001
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.725, 新色相: 0.2250000000000001
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8079999999999999
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.8079999999999999
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3919999999999999
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.3919999999999999
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #523F75
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #523F75
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.337] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EC5CF9
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #EC5CF9
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8194444444444443, 新色相: 0.3194444444444442
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.8194444444444443, 新色相: 0.3194444444444442
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9024444444444443
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: 0.083, 新色相: 0.9024444444444443
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.48644444444444446
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.48644444444444446
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EC5CF9
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #EC5CF9
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9B9B9B
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #9B9B9B
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.0, 新色相: 0.5
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.917
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.333
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9B9B9B
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #9B9B9B
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #077A6C
[2025-07-21 22:36:31.338] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #077A6C
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.48055555555555557, 新色相: 0.9805555555555556
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.48055555555555557, 新色相: 0.9805555555555556
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.39755555555555555
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.39755555555555555
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.14755555555555566
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.667, 新色相: 0.14755555555555566
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #077A6C
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #077A6C
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:61] 使用 RandomColorSwift 生成随机颜色组合
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #AF1126
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:70] 开始计算颜色组合，基准色: #AF1126
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:90] 开始计算互补色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9777777777777777, 新色相: 0.47777777777777786
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:106] 互补色计算完成，原色相: 0.9777777777777777, 新色相: 0.47777777777777786
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:128] 开始计算类似色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8947777777777778
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:148] 类似色计算完成，偏移: -0.083, 新色相: 0.8947777777777778
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:171] 开始计算三色调和色
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3107777777777778
[2025-07-21 22:36:31.339] [🐛 DEBUG] [📝General] [ColorUtils.swift:191] 三色调和色计算完成，偏移: 0.333, 新色相: 0.3107777777777778
[2025-07-21 22:36:31.340] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #AF1126
[2025-07-21 22:36:31.340] [🐛 DEBUG] [📝General] [ColorPaletteRow.swift:78] 随机颜色组合生成完成，基准色: #AF1126
[2025-07-21 22:36:31.340] [ℹ️ INFO] [📝General] [ColorPaletteRow.swift:165] 批量生成完成，共生成 100 个颜色组合
[2025-07-21 22:36:31.340] [ℹ️ INFO] [📝General] [ColorPaletteRow.swift:165] 批量生成完成，共生成 100 个颜色组合
[2025-07-21 22:36:31.340] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:318] 初始数据设置完成，共 100 个调色板
[2025-07-21 22:36:31.340] [ℹ️ INFO] [🎨UI] [WelcomeView.swift:318] 初始数据设置完成，共 100 个调色板
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: 0.0
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.340] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.352] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.352] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.370] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.370] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.386] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.386] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.404] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.404] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.420] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.420] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.436] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.436] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.453] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.453] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.470] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.470] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.486] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.486] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.502] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.502] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.519] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.519] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.536] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.536] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.552] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.552] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.569] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.569] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.570] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.570] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.586] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.586] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.602] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.602] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.619] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.619] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.636] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.636] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.653] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.653] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.669] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.669] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.703] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.703] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.719] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.719] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.736] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.736] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.752] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.752] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.769] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.769] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.786] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.786] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.803] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.803] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.820] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.820] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.836] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.836] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.865] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:373] 🚀 开始CADisplayLink驱动的自动滚动
[2025-07-21 22:36:31.865] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:373] 🚀 开始CADisplayLink驱动的自动滚动
[2025-07-21 22:36:31.865] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:24] 🎬 CADisplayLink 已启动 - 支持自适应刷新率
[2025-07-21 22:36:31.865] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:24] 🎬 CADisplayLink 已启动 - 支持自适应刷新率
[2025-07-21 22:36:31.870] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667
[2025-07-21 22:36:31.870] [🐛 DEBUG] [🎨UI] [WelcomeView.swift:199] xxx 用户滚动到位置: -97.66666666666667